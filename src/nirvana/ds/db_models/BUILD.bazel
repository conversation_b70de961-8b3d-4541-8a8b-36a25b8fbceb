load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "db_models",
    srcs = [
        "DAILY_MILEAGE_FEATURE_3MO_V1.go",
        "ENTROPY_MILEAGE_3MO_V1.go",
        "FLEET_MILEAGE_FEATURE_3MO_V1.go",
        "FLEET_STATE_MILEAGE_FEATURE_3MO_V1.go",
        "FLEET_ZONE_MILEAGE_FEATURE_3MO_V1.go",
        "FMCSA_FEATURES_3MO_V1.go",
        "HAZARD_MILES_3MO_V1.go",
        "VIN_COUNT_FEATURES_3MO_V1.go",
        "ZONE_ENTROPY_MILEAGE_3MO_V1.go",
        "boil_queries.go",
        "boil_table_names.go",
        "boil_types.go",
        "boil_view_names.go",
        "psql_upsert.go",
    ],
    importpath = "nirvanatech.com/nirvana/ds/db_models",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_friendsofgo_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//drivers",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//queries/qmhelper",
        "@com_github_volatiletech_sqlboiler_v4//types",
        "@com_github_volatiletech_strmangle//:strmangle",
    ],
)
