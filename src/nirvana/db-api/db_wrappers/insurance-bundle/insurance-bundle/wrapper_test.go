package insurancebundle_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/benbjo<PERSON>son/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	protolib "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	ibsegment "nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/ib-segment"
	insurancebundle "nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle"
	"nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/policyv2"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
)

func TestInsertInsuranceBundle(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		Wrapper insurancebundle.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	mockInsuranceBundle := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).Build()

	err := deps.Wrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)

	// Insert fails because of duplicate id
	err = deps.Wrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.Error(t, err)
	assert.ErrorIs(t, err, insurancebundle.ErrDuplicateInternalId)

	// Insert fails because of duplicate externalID, version, programType
	mockInsuranceBundle = model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).WithInternalId(uuid.NewString()).Build()
	err = deps.Wrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.Error(t, err)
	assert.ErrorIs(t, err, insurancebundle.ErrDuplicateExternalIdAndVersion)

	// Insert does not fail if externalID, version are same but programType is different
	mockInsuranceBundle = model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithInternalId(uuid.NewString()).Build()
	err = deps.Wrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)
}

func TestGetByInternalId(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		MockClock        *clock.Mock
		IBWrapper        insurancebundle.Wrapper
		IBSegmentWrapper ibsegment.Wrapper
		PolicyWrapper    policyv2.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	segments := getIBSegmentsForTesting()
	mockInsuranceBundle := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).WithSegments(segments).WithNoticeOfCancellation().Build()
	ibSegments, policies := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(mockInsuranceBundle)
	// randomize the order of segments to be inserted into the DB. Note that the IB still contains these segments in
	// order of their intervals.
	slice_utils.Shuffle(ibSegments)

	err := deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)
	err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments)
	require.NoError(t, err)
	err = deps.PolicyWrapper.InsertPolicies(ctx, policies)
	require.NoError(t, err)

	ib, err := deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.NotNil(t, ib)
	mockInsuranceBundle.CreatedAt = timestamppb.New(deps.MockClock.Now())
	mockInsuranceBundle.UpdatedAt = timestamppb.New(deps.MockClock.Now())
	require.EqualExportedValues(t, mockInsuranceBundle, ib)

	// not found in case IB does not exist
	ib, err = deps.IBWrapper.GetByInternalId(ctx, uuid.NewString())
	assert.Error(t, err)
	assert.ErrorIs(t, err, insurancebundle.ErrNotFound)
	assert.Nil(t, ib)

	// no segments in IB when segments are not inserted
	mockInsuranceBundle = model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInternalId(uuid.NewString()).
		WithExternalId("mock-ext-id-1").
		Build()
	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)

	ib, err = deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.NotNil(t, ib)
	require.Nil(t, ib.Segments)

	// no policies in IB segments when policies are not inserted
	mockInsuranceBundle = model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInternalId(uuid.NewString()).
		WithExternalId("mock-ext-id-2").
		Build()
	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)

	ibSegments, _ = insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(mockInsuranceBundle)
	err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments)
	require.NoError(t, err)

	ib, err = deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.NotNil(t, ib)
	require.Len(t, ib.Segments, 2)
	require.Empty(t, ib.Segments[0].Policies)
	require.Empty(t, ib.Segments[1].Policies)
}

func TestGetVersionForInternalId(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		IBWrapper        insurancebundle.Wrapper
		IBSegmentWrapper ibsegment.Wrapper
		PolicyWrapper    policyv2.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	mockInsuranceBundle := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithVersion(420).Build()

	err := deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)

	version, err := deps.IBWrapper.GetVersionForInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.Equal(t, mockInsuranceBundle.Version, version)

	// not found in case IB does not exist
	version, err = deps.IBWrapper.GetVersionForInternalId(ctx, uuid.NewString())
	assert.Error(t, err)
	assert.ErrorIs(t, err, insurancebundle.ErrNotFound)
	assert.Equal(t, int64(0), version)
}

// TODO: Add test for filters using Sarthak's method.
func TestGetInsuranceBundles(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		MockClock        *clock.Mock
		IBWrapper        insurancebundle.Wrapper
		IBSegmentWrapper ibsegment.Wrapper
		PolicyWrapper    policyv2.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	// not found in case no IBs exist
	ibs, err := deps.IBWrapper.GetInsuranceBundles(ctx)
	assert.Error(t, err)
	assert.Len(t, ibs, 0)
	assert.ErrorIs(t, err, insurancebundle.ErrNotFound)

	segments := getIBSegmentsForTesting()
	mockInsuranceBundle1 := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).WithSegments(segments).Build()
	ibSegments1, policies1 := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(mockInsuranceBundle1)
	// randomize the order of segments to be inserted into the DB. Note that the IB still contains these segments in
	// order of their intervals.
	slice_utils.Shuffle(ibSegments1)
	mockInsuranceBundle2 := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInternalId(uuid.NewString()).
		WithExternalId("mock-ext-id-1").
		Build()
	ibSegments2, policies2 := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(mockInsuranceBundle2)

	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle1)
	require.NoError(t, err)
	err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments1)
	require.NoError(t, err)
	err = deps.PolicyWrapper.InsertPolicies(ctx, policies1)
	require.NoError(t, err)
	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle2)
	require.NoError(t, err)
	err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments2)
	require.NoError(t, err)
	err = deps.PolicyWrapper.InsertPolicies(ctx, policies2)
	require.NoError(t, err)

	ibs, err = deps.IBWrapper.GetInsuranceBundles(ctx)
	require.NoError(t, err)
	require.Len(t, ibs, 2)

	containsFn := func(ib *model.InsuranceBundle) bool {
		for _, ib1 := range ibs {
			if protolib.Equal(ib1, ib) {
				return true
			}
		}
		return false
	}

	mockInsuranceBundle1.CreatedAt = timestamppb.New(deps.MockClock.Now())
	mockInsuranceBundle1.UpdatedAt = timestamppb.New(deps.MockClock.Now())
	assert.True(t, containsFn(mockInsuranceBundle1))
	mockInsuranceBundle2.CreatedAt = timestamppb.New(deps.MockClock.Now())
	mockInsuranceBundle2.UpdatedAt = timestamppb.New(deps.MockClock.Now())
	assert.True(t, containsFn(mockInsuranceBundle2))
}

func TestGetCondensedInsuranceBundles(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		IBWrapper        insurancebundle.Wrapper
		IBSegmentWrapper ibsegment.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	// not found in case no IBs exist
	ibs, err := deps.IBWrapper.GetCondensedInsuranceBundles(ctx)
	assert.NoError(t, err)
	assert.Len(t, ibs, 0)

	segments := getIBSegmentsForTesting()
	mockInsuranceBundle1 := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithSegments(segments).Build()
	ibSegments1, _ := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(mockInsuranceBundle1)
	// randomize the order of segments to be inserted into the DB. Note that the IB still contains these segments in
	// order of their intervals.
	slice_utils.Shuffle(ibSegments1)
	mockInsuranceBundle2 := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInternalId(uuid.NewString()).
		WithExternalId("mock-ext-id-1").
		Build()
	ibSegments2, _ := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(mockInsuranceBundle2)

	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle1)
	require.NoError(t, err)
	err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments1)
	require.NoError(t, err)
	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle2)
	require.NoError(t, err)
	err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments2)
	require.NoError(t, err)

	ibs, err = deps.IBWrapper.GetCondensedInsuranceBundles(ctx)
	require.NoError(t, err)
	require.Len(t, ibs, 2)
	assert.True(t, equalIgnoreTimestamps(mockInsuranceBundle1.GetCondensed(), ibs[0]))
	assert.True(t, equalIgnoreTimestamps(mockInsuranceBundle2.GetCondensed(), ibs[1]))
}

func TestListInsuranceBundle_Combinatorial(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	var deps struct {
		fx.In
		MockClock        *clock.Mock
		IBWrapper        insurancebundle.Wrapper
		IBSegmentWrapper ibsegment.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	type filter struct {
		name         string
		dbVersion    insurancebundle.Filter
		dbVersions   []insurancebundle.Filter
		inMemVersion func(bundle *model.InsuranceBundle) bool
	}
	type paginationFilter struct {
		name         string
		pagination   insurancebundle.Filter
		sort         insurancebundle.Filter
		limit        int
		inMemVersion func(bundle *model.InsuranceBundle) bool
	}

	interval := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2022, 12, 31).ToTime()),
	}

	dataset := []*model.InsuranceBundle{
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithVersion(int64(1)).
			Build(),
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
			WithInternalId(uuid.NewString()).
			WithExternalId("mock-ext-id-1").
			Build(),
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithInternalId(uuid.NewString()).
			WithExternalId("mock-ext-id-2").Build(),
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
			WithInternalId(uuid.NewString()).
			WithExternalId("mock-ext-id-3").Build(),
	}
	baseTime := deps.MockClock.Now()
	baseTime = baseTime.Add(48 * time.Hour)

	filtersForPagination := []paginationFilter{
		{
			name: "CreatedAtExternalID greater than pagination filter - sort ascending",
			pagination: insurancebundle.NewCreatedAtExternalIDPaginationCursorFilter(
				dataset[2].ExternalId, baseTime, false),
			sort:  insurancebundle.NewDefaultOrderByForStablePaginationFilter(false),
			limit: 2,
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.CreatedAt.AsTime().After(baseTime)
			},
		},
		{
			name: "CreatedAtExternalID less than pagination filter - sort descending",
			pagination: insurancebundle.NewCreatedAtExternalIDPaginationCursorFilter(
				dataset[2].ExternalId, baseTime, true),
			sort:  insurancebundle.NewDefaultOrderByForStablePaginationFilter(true),
			limit: 2,
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.CreatedAt.AsTime().Before(baseTime)
			},
		},
	}

	filtersToTest := []filter{
		{
			name:      "version",
			dbVersion: insurancebundle.VersionIs(1),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.GetVersion() == int64(1)
			},
		},
		{
			name:      "external id",
			dbVersion: insurancebundle.NewExternalIdFilter(dataset[2].ExternalId),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.GetExternalId() == dataset[2].ExternalId
			},
		},
		{
			name:      "program types in",
			dbVersion: insurancebundle.NewProgramTypesInFilter([]insurancecoreproto.ProgramType{insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted}),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.ProgramType == insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted
			},
		},
		{
			name:       "Interval",
			dbVersions: insurancebundle.BuildIntervalFilters(interval),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.CreatedAt.AsTime().After(interval.Start.AsTime()) && ib.CreatedAt.AsTime().Before(interval.End.AsTime())
			},
		},
	}
	// Insert the dataset into the DB
	for i, ib := range dataset {
		err := deps.IBWrapper.InsertInsuranceBundle(ctx, ib)
		ib.CreatedAt = timestamppb.New(deps.MockClock.Now())
		ib.UpdatedAt = timestamppb.New(deps.MockClock.Now())
		require.NoErrorf(t, err, "index %d", i)

		ibSegments, _ := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(ib)
		err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments)
		deps.MockClock.Add(time.Hour * 24) // to ensure CreatedAt timestamps are different
	}

	attachDescendingFilter := false // toggle to attach descending/ascending filter in the filtersToTest loop
	for mask := 0; mask < (1 << len(filtersToTest)); mask++ {
		t.Run(fmt.Sprintf("%08b", mask), func(t *testing.T) {
			expectedDataset := dataset
			var filtersToApply []insurancebundle.Filter
			for i := range filtersToTest {
				if (1<<i)&mask > 0 {
					if filtersToTest[i].dbVersions != nil {
						filtersToApply = append(filtersToApply, filtersToTest[i].dbVersions...)
					} else {
						filtersToApply = append(filtersToApply, filtersToTest[i].dbVersion)
					}
					expectedDataset = slice_utils.Filter(expectedDataset, filtersToTest[i].inMemVersion)
				}
			}

			// apply pagination filters
			var pageFilter paginationFilter
			if attachDescendingFilter {
				pageFilter = filtersForPagination[1]
			} else {
				pageFilter = filtersForPagination[0]
			}
			filtersToApply = append(filtersToApply, pageFilter.pagination)
			filtersToApply = append(filtersToApply, pageFilter.sort)
			expectedDataset = slice_utils.Filter(expectedDataset, pageFilter.inMemVersion)

			// toggle for next iteration
			attachDescendingFilter = !attachDescendingFilter

			actual, err := deps.IBWrapper.GetInsuranceBundles(ctx, filtersToApply...)
			if err != nil && len(expectedDataset) == 0 {
				assert.ErrorIs(t, err, insurancebundle.ErrNotFound)
				return

			}
			require.NoError(t, err)

			clearSegments := func(ib *model.InsuranceBundle) *model.InsuranceBundle {
				ib.Segments = nil
				return ib
			}
			expectedDataset = slice_utils.Map(expectedDataset, clearSegments)
			actual = slice_utils.Map(actual, clearSegments)

			assert.ElementsMatch(t, expectedDataset, actual)
		})
	}
}

func TestGetCondensedInsuranceBundles_Combinatorial(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		MockClock        *clock.Mock
		IBWrapper        insurancebundle.Wrapper
		IBSegmentWrapper ibsegment.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	mockSellers := []*insurancecoreproto.SellerInfo{
		{
			AgencyID: uuid.NewString(),
		},
		{
			AgencyID: uuid.NewString(),
		},
	}

	type filter struct {
		name         string
		dbVersion    insurancebundle.Filter
		inMemVersion func(bundle *model.InsuranceBundle) bool
	}

	type paginationFilter struct {
		name         string
		pagination   insurancebundle.Filter
		sort         insurancebundle.Filter
		inMemVersion func(bundle *model.InsuranceBundle) bool
	}

	dataset := []*model.InsuranceBundle{
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithAgencyID(mockSellers[0].GetAgencyID()).
			Build(),
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).WithInternalId(uuid.NewString()).
			WithExternalId("mock-ext-id-1").WithAgencyID(mockSellers[0].GetAgencyID()).
			Build(),
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			WithInternalId(uuid.NewString()).
			WithExternalId("mock-ext-id-2").WithAgencyID(mockSellers[1].GetAgencyID()).Build(),
		model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).WithInternalId(uuid.NewString()).
			WithExternalId("mock-ext-id-3").Build(),
	}

	baseTime := deps.MockClock.Now()
	baseTime = baseTime.Add(48 * time.Hour)

	filtersForPagination := []paginationFilter{
		{
			name: "CreatedAtExternalID greater than pagination filter - sort ascending",
			pagination: insurancebundle.NewCreatedAtExternalIDPaginationCursorFilter(
				dataset[2].ExternalId, baseTime, false),
			sort: insurancebundle.NewDefaultOrderByForStablePaginationFilter(false),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.CreatedAt.AsTime().After(baseTime)
			},
		},
		{
			name: "CreatedAtExternalID less than pagination filter - sort descending",
			pagination: insurancebundle.NewCreatedAtExternalIDPaginationCursorFilter(
				dataset[2].ExternalId, baseTime, true),
			sort: insurancebundle.NewDefaultOrderByForStablePaginationFilter(true),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.CreatedAt.AsTime().Before(baseTime)
			},
		},
	}

	filtersToTest := []filter{
		{
			name:      "seller",
			dbVersion: insurancebundle.NewSellerFilter(mockSellers[0]),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return protolib.Equal(ib.GetDefaultSeller(), mockSellers[0])
			},
		},
		{
			name:      "program types in",
			dbVersion: insurancebundle.NewProgramTypesInFilter([]insurancecoreproto.ProgramType{insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted}),
			inMemVersion: func(ib *model.InsuranceBundle) bool {
				return ib.ProgramType == insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted
			},
		},
	}

	for i, ib := range dataset {
		err := deps.IBWrapper.InsertInsuranceBundle(ctx, ib)
		ib.CreatedAt = timestamppb.New(deps.MockClock.Now())
		ib.UpdatedAt = timestamppb.New(deps.MockClock.Now())
		require.NoErrorf(t, err, "index %d", i)

		ibSegments, _ := insurancebundle.ConstructIBSegmentsAndPoliciesDBRepresentations(ib)
		err = deps.IBSegmentWrapper.InsertIBSegments(ctx, ibSegments)
		deps.MockClock.Add(time.Hour * 24) // to ensure CreatedAt timestamps are different
	}

	attachDescendingFilter := false // toggle to attach descending/ascending filter in the filtersToTest loop
	for mask := 0; mask < (1 << len(filtersToTest)); mask++ {
		t.Run(fmt.Sprintf("%08b", mask), func(t *testing.T) {
			expectedDataset := dataset
			var filtersToApply []insurancebundle.Filter
			for i := range filtersToTest {
				if (1<<i)&mask > 0 {
					filtersToApply = append(filtersToApply, filtersToTest[i].dbVersion)
					expectedDataset = slice_utils.Filter(expectedDataset, filtersToTest[i].inMemVersion)
				}
			}

			// apply interval filter
			var pageFilter paginationFilter
			if attachDescendingFilter {
				pageFilter = filtersForPagination[1]
			} else {
				pageFilter = filtersForPagination[0]
			}
			filtersToApply = append(filtersToApply, pageFilter.pagination)
			filtersToApply = append(filtersToApply, pageFilter.sort)
			expectedDataset = slice_utils.Filter(expectedDataset, pageFilter.inMemVersion)

			// toggle for next iteration
			attachDescendingFilter = !attachDescendingFilter

			actual, err := deps.IBWrapper.GetCondensedInsuranceBundles(ctx, filtersToApply...)
			require.NoError(t, err)

			condensedConverterFn := func(ib *model.InsuranceBundle) *model.CondensedInsuranceBundle {
				return ib.GetCondensed()
			}
			expectedDatasetCondensed := slice_utils.Map(expectedDataset, condensedConverterFn)

			assert.ElementsMatch(t, expectedDatasetCondensed, actual)
		})
	}
}

func TestUpdateInsuranceBundlesState(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		IBWrapper insurancebundle.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	mockInsuranceBundle := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).Build()
	err := deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)

	err = deps.IBWrapper.UpdateInsuranceBundlesState(ctx, []string{mockInsuranceBundle.InternalId}, model.InsuranceBundleState_InsuranceBundleState_Stale)
	require.NoError(t, err)

	ib, err := deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.Equal(t, model.InsuranceBundleState_InsuranceBundleState_Stale, ib.State)

	// update multiple IBs
	mockInsuranceBundle2 := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInternalId(uuid.NewString()).
		WithExternalId("mock-ext-id-1").
		Build()
	err = deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle2)
	require.NoError(t, err)

	err = deps.IBWrapper.UpdateInsuranceBundlesState(ctx, []string{mockInsuranceBundle.InternalId, mockInsuranceBundle2.InternalId}, model.InsuranceBundleState_InsuranceBundleState_Active)
	require.NoError(t, err)

	ib, err = deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.Equal(t, model.InsuranceBundleState_InsuranceBundleState_Active, ib.State)

	ib, err = deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle2.InternalId)
	require.NoError(t, err)
	require.Equal(t, model.InsuranceBundleState_InsuranceBundleState_Active, ib.State)
}

func getIBSegmentsForTesting() []*model.InsuranceBundleSegment {
	interval1 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 3, 1).ToTime()),
	}
	segment1 := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInterval(interval1).Build()
	interval2 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2025, 3, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 6, 1).ToTime()),
	}
	segment2 := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInterval(interval2).Build()
	interval3 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2025, 6, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 9, 1).ToTime()),
	}
	segment3 := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInterval(interval3).Build()
	interval4 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2025, 9, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 12, 1).ToTime()),
	}
	segment4 := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		WithInterval(interval4).Build()

	return []*model.InsuranceBundleSegment{segment1, segment2, segment3, segment4}
}

func equalIgnoreTimestamps(a, b *model.CondensedInsuranceBundle) bool {
	// Clear timestamps
	a.CreatedAt = nil
	a.UpdatedAt = nil
	b.CreatedAt = nil
	b.UpdatedAt = nil

	return protolib.Equal(a, b)
}

// TestUpdateInsuranceBundleDefaultEffectiveDurationEnd tests the UpdateInsuranceBundleDefaultEffectiveDurationEnd method of the insurance bundle wrapper.
func TestUpdateInsuranceBundleDefaultEffectiveDurationEnd(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	var deps struct {
		fx.In
		IBWrapper insurancebundle.Wrapper
	}
	defer testloader.RequireStart(t, &deps).RequireStop()

	mockInsuranceBundle := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).Build()
	err := deps.IBWrapper.InsertInsuranceBundle(ctx, mockInsuranceBundle)
	require.NoError(t, err)

	defaultEffectiveDurationEnd := time_utils.NewDate(2025, 12, 1).ToTime()
	err = deps.IBWrapper.UpdateInsuranceBundleDefaultEffectiveDurationEnd(ctx, mockInsuranceBundle.InternalId, defaultEffectiveDurationEnd)
	require.NoError(t, err)

	ib, err := deps.IBWrapper.GetByInternalId(ctx, mockInsuranceBundle.InternalId)
	require.NoError(t, err)
	require.Equal(t, defaultEffectiveDurationEnd, ib.DefaultEffectiveDuration.End.AsTime())
}
