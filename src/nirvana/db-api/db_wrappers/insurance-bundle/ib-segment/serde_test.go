package ibsegment

import (
	"testing"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/db-api/db_models/insurance_bundle"
	"nirvanatech.com/nirvana/insurance-bundle/model"
)

func TestSerde(t *testing.T) {
	t.Parallel()
	noc := &model.NoticeOfCancellation{
		PrimaryInsuredNOC: &model.PrimaryInsuredNOC{
			NocDetails: &model.NOCDetails{
				DaysForNonPaymentNOC: pointer_utils.Int32(20),
				DaysForOtherNOCs:     pointer_utils.Int32(40),
			},
		},
		NonPrimaryNOCs: []*model.NonPrimaryNOC{
			{
				Name: "Builder Test Entity",
				Address: &proto.Address{
					Street:  pointer_utils.String("456 Builder St"),
					City:    pointer_utils.String("Builder City"),
					State:   pointer_utils.String("NY"),
					ZipCode: pointer_utils.String("67890"),
				},
				NocDetails: &model.NOCDetails{
					DaysForNonPaymentNOC: pointer_utils.Int32(25),
					DaysForOtherNOCs:     pointer_utils.Int32(50),
				},
			},
		},
	}

	ibSegmentDBRepr := IBSegmentDBRepresentation{
		IBID: uuid.NewString(),
		IBSegment: model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
			UnsetPolicies().WithNoticeOfCancellation(noc).Build(),
	}

	dbModel, err := convertIBSegmentDBRepresentationToIBSegmentDBModel(ibSegmentDBRepr)
	require.NoError(t, err)
	require.NotNil(t, dbModel)

	unmarshalledIBSegment, err := ConvertIBSegmentDBModelToIBSegmentProto(dbModel)
	require.NoError(t, err)
	require.NotNil(t, unmarshalledIBSegment)

	require.Equal(t, ibSegmentDBRepr.IBSegment, unmarshalledIBSegment)
}

func TestSerdeWithNoticeOfCancellation(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name                 string
		noticeOfCancellation *model.NoticeOfCancellation
		checkResult          func(t *testing.T, original, result *model.InsuranceBundleSegment)
	}{
		{
			name:                 "nil notice of cancellation",
			noticeOfCancellation: nil,
			checkResult: func(t *testing.T, original, result *model.InsuranceBundleSegment) {
				// Result should have empty NOC structure due to unmarshaling
				assert.True(t, result.NoticeOfCancellation == nil ||
					(result.NoticeOfCancellation.PrimaryInsuredNOC == nil &&
						len(result.NoticeOfCancellation.NonPrimaryNOCs) == 0))
			},
		},
		{
			name: "empty notice of cancellation",
			noticeOfCancellation: &model.NoticeOfCancellation{
				PrimaryInsuredNOC: nil,
				NonPrimaryNOCs:    []*model.NonPrimaryNOC{},
			},
			checkResult: func(t *testing.T, original, result *model.InsuranceBundleSegment) {
				assert.NotNil(t, result.NoticeOfCancellation)
				assert.Nil(t, result.NoticeOfCancellation.PrimaryInsuredNOC)
				assert.Empty(t, result.NoticeOfCancellation.NonPrimaryNOCs)
			},
		},
		{
			name: "populated notice of cancellation",
			noticeOfCancellation: &model.NoticeOfCancellation{
				PrimaryInsuredNOC: &model.PrimaryInsuredNOC{
					NocDetails: &model.NOCDetails{
						DaysForNonPaymentNOC: pointer_utils.Int32(10),
						DaysForOtherNOCs:     pointer_utils.Int32(30),
					},
				},
				NonPrimaryNOCs: []*model.NonPrimaryNOC{
					{
						Name: "Test Entity",
						Address: &proto.Address{
							Street:  pointer_utils.String("123 Test St"),
							City:    pointer_utils.String("Test City"),
							State:   pointer_utils.String("CA"),
							ZipCode: pointer_utils.String("12345"),
						},
						NocDetails: &model.NOCDetails{
							DaysForNonPaymentNOC: pointer_utils.Int32(15),
							DaysForOtherNOCs:     pointer_utils.Int32(45),
						},
					},
				},
			},
			checkResult: func(t *testing.T, original, result *model.InsuranceBundleSegment) {
				assert.NotNil(t, result.NoticeOfCancellation)
				assert.NotNil(t, result.NoticeOfCancellation.PrimaryInsuredNOC)
				assert.NotNil(t, result.NoticeOfCancellation.PrimaryInsuredNOC.NocDetails)
				assert.Equal(t, int32(10), *result.NoticeOfCancellation.PrimaryInsuredNOC.NocDetails.DaysForNonPaymentNOC)
				assert.Equal(t, int32(30), *result.NoticeOfCancellation.PrimaryInsuredNOC.NocDetails.DaysForOtherNOCs)

				require.Len(t, result.NoticeOfCancellation.NonPrimaryNOCs, 1)
				nonPrimaryNOC := result.NoticeOfCancellation.NonPrimaryNOCs[0]
				assert.Equal(t, "Test Entity", nonPrimaryNOC.Name)
				assert.Equal(t, "123 Test St", *nonPrimaryNOC.Address.Street)
				assert.Equal(t, "Test City", *nonPrimaryNOC.Address.City)
				assert.Equal(t, "CA", *nonPrimaryNOC.Address.State)
				assert.Equal(t, "12345", *nonPrimaryNOC.Address.ZipCode)
				assert.Equal(t, int32(15), *nonPrimaryNOC.NocDetails.DaysForNonPaymentNOC)
				assert.Equal(t, int32(45), *nonPrimaryNOC.NocDetails.DaysForOtherNOCs)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create segment with the specified NoticeOfCancellation
			segment := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
				UnsetPolicies().
				Build()
			segment.NoticeOfCancellation = tc.noticeOfCancellation

			ibSegmentDBRepr := IBSegmentDBRepresentation{
				IBID:      uuid.NewString(),
				IBSegment: segment,
			}

			// Convert to DB model
			dbModel, err := convertIBSegmentDBRepresentationToIBSegmentDBModel(ibSegmentDBRepr)
			require.NoError(t, err)
			require.NotNil(t, dbModel)

			// Convert back to proto
			unmarshalledIBSegment, err := ConvertIBSegmentDBModelToIBSegmentProto(dbModel)
			require.NoError(t, err)
			require.NotNil(t, unmarshalledIBSegment)

			// Verify basic fields
			assert.Equal(t, ibSegmentDBRepr.IBSegment.Id, unmarshalledIBSegment.Id)
			assert.Equal(t, ibSegmentDBRepr.IBSegment.Interval, unmarshalledIBSegment.Interval)
			assert.Equal(t, ibSegmentDBRepr.IBSegment.PrimaryInsured, unmarshalledIBSegment.PrimaryInsured)
			assert.Equal(t, ibSegmentDBRepr.IBSegment.Policies, unmarshalledIBSegment.Policies)
			assert.Equal(t, ibSegmentDBRepr.IBSegment.CoverageCriteria, unmarshalledIBSegment.CoverageCriteria)

			// Use custom check for NoticeOfCancellation
			tc.checkResult(t, ibSegmentDBRepr.IBSegment, unmarshalledIBSegment)
		})
	}
}

func TestSerdeWithBuilderNoticeOfCancellation(t *testing.T) {
	t.Parallel()

	// Test using the builder method
	noc := &model.NoticeOfCancellation{
		PrimaryInsuredNOC: &model.PrimaryInsuredNOC{
			NocDetails: &model.NOCDetails{
				DaysForNonPaymentNOC: pointer_utils.Int32(20),
				DaysForOtherNOCs:     pointer_utils.Int32(40),
			},
		},
		NonPrimaryNOCs: []*model.NonPrimaryNOC{
			{
				Name: "Builder Test Entity",
				Address: &proto.Address{
					Street:  pointer_utils.String("456 Builder St"),
					City:    pointer_utils.String("Builder City"),
					State:   pointer_utils.String("NY"),
					ZipCode: pointer_utils.String("67890"),
				},
				NocDetails: &model.NOCDetails{
					DaysForNonPaymentNOC: pointer_utils.Int32(25),
					DaysForOtherNOCs:     pointer_utils.Int32(50),
				},
			},
		},
	}

	segment := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		UnsetPolicies().
		WithNoticeOfCancellation(noc).
		Build()

	ibSegmentDBRepr := IBSegmentDBRepresentation{
		IBID:      uuid.NewString(),
		IBSegment: segment,
	}

	// Convert to DB model
	dbModel, err := convertIBSegmentDBRepresentationToIBSegmentDBModel(ibSegmentDBRepr)
	require.NoError(t, err)
	require.NotNil(t, dbModel)

	// Convert back to proto
	unmarshalledIBSegment, err := ConvertIBSegmentDBModelToIBSegmentProto(dbModel)
	require.NoError(t, err)
	require.NotNil(t, unmarshalledIBSegment)

	// Verify NoticeOfCancellation was preserved correctly
	assert.NotNil(t, unmarshalledIBSegment.NoticeOfCancellation)
	assert.NotNil(t, unmarshalledIBSegment.NoticeOfCancellation.PrimaryInsuredNOC)
	assert.NotNil(t, unmarshalledIBSegment.NoticeOfCancellation.PrimaryInsuredNOC.NocDetails)
	assert.Equal(t, int32(20), *unmarshalledIBSegment.NoticeOfCancellation.PrimaryInsuredNOC.NocDetails.DaysForNonPaymentNOC)
	assert.Equal(t, int32(40), *unmarshalledIBSegment.NoticeOfCancellation.PrimaryInsuredNOC.NocDetails.DaysForOtherNOCs)

	require.Len(t, unmarshalledIBSegment.NoticeOfCancellation.NonPrimaryNOCs, 1)
	nonPrimaryNOC := unmarshalledIBSegment.NoticeOfCancellation.NonPrimaryNOCs[0]
	assert.Equal(t, "Builder Test Entity", nonPrimaryNOC.Name)
	assert.Equal(t, "456 Builder St", *nonPrimaryNOC.Address.Street)
	assert.Equal(t, "Builder City", *nonPrimaryNOC.Address.City)
	assert.Equal(t, "NY", *nonPrimaryNOC.Address.State)
	assert.Equal(t, "67890", *nonPrimaryNOC.Address.ZipCode)
	assert.Equal(t, int32(25), *nonPrimaryNOC.NocDetails.DaysForNonPaymentNOC)
	assert.Equal(t, int32(50), *nonPrimaryNOC.NocDetails.DaysForOtherNOCs)
}

func TestSerdeErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("nil db model", func(t *testing.T) {
		result, err := ConvertIBSegmentDBModelToIBSegmentProto(nil)
		require.Error(t, err)
		require.Nil(t, result)
		assert.Contains(t, err.Error(), "nil insurance bundle segment")
	})

	t.Run("invalid primary insured JSON", func(t *testing.T) {
		dbModel := &insurance_bundle.InsuranceBundleSegment{
			ID:                "test-id",
			InsuranceBundleID: "test-ib-id",
			PrimaryInsured:    []byte("invalid json"),
			CoverageCriteria:  []byte("{}"),
		}

		result, err := ConvertIBSegmentDBModelToIBSegmentProto(dbModel)
		require.Error(t, err)
		require.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to unmarshal primary insured")
	})

	t.Run("invalid coverage criteria JSON", func(t *testing.T) {
		dbModel := &insurance_bundle.InsuranceBundleSegment{
			ID:                "test-id",
			InsuranceBundleID: "test-ib-id",
			PrimaryInsured:    []byte("{}"),
			CoverageCriteria:  []byte("invalid json"),
		}

		result, err := ConvertIBSegmentDBModelToIBSegmentProto(dbModel)
		require.Error(t, err)
		require.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to unmarshal coverage criteria")
	})

	t.Run("invalid notice of cancellation JSON", func(t *testing.T) {
		dbModel := &insurance_bundle.InsuranceBundleSegment{
			ID:                   "test-id",
			InsuranceBundleID:    "test-ib-id",
			PrimaryInsured:       []byte("{}"),
			CoverageCriteria:     []byte("{}"),
			NoticeOfCancellation: null.JSONFrom([]byte("invalid json")),
		}

		result, err := ConvertIBSegmentDBModelToIBSegmentProto(dbModel)
		require.Error(t, err)
		require.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to unmarshal notice of cancellation")
	})
}
