load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "ib-segment",
    srcs = [
        "filters.go",
        "fx.go",
        "interface.go",
        "serde.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/ib-segment",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/insurance_bundle",
        "//nirvana/db-api/db_wrappers/common",
        "//nirvana/db-api/db_wrappers/insurance-bundle/common/filter",
        "//nirvana/db-api/db_wrappers/insurance-bundle/policyv2",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-core/proto",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "ib-segment_test",
    srcs = [
        "serde_test.go",
        "wrapper_test.go",
    ],
    embed = [":ib-segment"],
    deps = [
        "//nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle",
        "//nirvana/infra/fx/testloader",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-core/proto",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
