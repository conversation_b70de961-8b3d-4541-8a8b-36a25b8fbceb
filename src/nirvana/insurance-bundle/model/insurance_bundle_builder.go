package model

import (
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type InsuranceBundleBuilder struct {
	ib *InsuranceBundle
}

// NewInsuranceBundleBuilder gives you a new InsuranceBundleBuilder with default values. You can override these values
// by using the With* methods.
func NewInsuranceBundleBuilder(programType insurancecoreproto.ProgramType) *InsuranceBundleBuilder {
	internalID := uuid.MustParse("079c676a-ad3b-405e-9c7f-9c1eb0622634")
	agencyID := uuid.MustParse("6a7acecc-e3cf-537d-8942-84b57d65e7e6")
	interval1 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 5, 1).ToTime()),
	}
	interval2 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2025, 5, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 12, 1).ToTime()),
	}
	ibSegment1 := NewInsuranceBundleSegmentBuilder(programType).WithInterval(interval1).Build()
	ibSegment2 := NewInsuranceBundleSegmentBuilder(programType).WithInterval(interval2).Build()

	return &InsuranceBundleBuilder{
		ib: &InsuranceBundle{
			ExternalId:          "mock-id",
			InternalId:          internalID.String(),
			Version:             0,
			DefaultCarrier:      insurancecoreproto.InsuranceCarrier_InsuranceCarrier_SiriusPoint,
			CarrierAdmittedType: insurancecoreproto.CarrierAdmittedType_CarrierAdmittedType_Admitted,
			DefaultSeller: &insurancecoreproto.SellerInfo{
				AgencyID: agencyID.String(),
			},
			DefaultEffectiveDuration: &proto.Interval{
				Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
				End:   timestamppb.New(time_utils.NewDate(2025, 12, 1).ToTime()),
			},
			ProgramType: programType,
			Metadata: &InsuranceBundleMetadata{
				RootBindableSubmissionId:      uuid.NewString(),
				RootApplicationId:             uuid.NewString(),
				EndorsementApplicationIDs:     []string{uuid.NewString()},
				EndorsementApplicationIDDelta: pointer_utils.ToPointer(uuid.NewString()),
			},
			FormInfo: &insurancecoreproto.FormInfo{
				CoreForm: &insurancecoreproto.FormCore{
					FormCompilationId:   uuid.NewString(),
					FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
					DocumentHandleId:    uuid.NewString(),
					FormCodes:           []string{"sp-form-1", "sp-form-2"},
				},
			},
			Segments:  []*InsuranceBundleSegment{ibSegment1, ibSegment2},
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
			State:     InsuranceBundleState_InsuranceBundleState_Active,
		},
	}
}

func (i *InsuranceBundleBuilder) Build() *InsuranceBundle {
	return i.ib
}

func (i *InsuranceBundleBuilder) WithSegments(segments []*InsuranceBundleSegment) *InsuranceBundleBuilder {
	i.ib.Segments = segments
	return i
}

func (i *InsuranceBundleBuilder) WithAgencyID(agencyID string) *InsuranceBundleBuilder {
	i.ib.DefaultSeller.AgencyID = agencyID
	return i
}

func (i *InsuranceBundleBuilder) WithInternalId(internalId string) *InsuranceBundleBuilder {
	i.ib.InternalId = internalId
	return i
}

func (i *InsuranceBundleBuilder) WithExternalId(externalId string) *InsuranceBundleBuilder {
	i.ib.ExternalId = externalId
	return i
}

func (i *InsuranceBundleBuilder) WithCreatedAt(createdAt *timestamppb.Timestamp) *InsuranceBundleBuilder {
	i.ib.CreatedAt = createdAt
	return i
}

func (i *InsuranceBundleBuilder) WithUpdatedAt(updatedAt *timestamppb.Timestamp) *InsuranceBundleBuilder {
	i.ib.UpdatedAt = updatedAt
	return i
}

func (i *InsuranceBundleBuilder) WithRootApplicationId(appId uuid.UUID) *InsuranceBundleBuilder {
	i.ib.Metadata.RootApplicationId = appId.String()
	return i
}

func (i *InsuranceBundleBuilder) WithRootBindableSubmissionId(subId uuid.UUID) *InsuranceBundleBuilder {
	i.ib.Metadata.RootBindableSubmissionId = subId.String()
	return i
}

func (i *InsuranceBundleBuilder) WithCoreForm(coreForm *insurancecoreproto.FormCore) *InsuranceBundleBuilder {
	i.ib.FormInfo.CoreForm = coreForm
	return i
}

func (i *InsuranceBundleBuilder) WithAdditionalForms(additionalForms []*insurancecoreproto.FormCore) *InsuranceBundleBuilder {
	i.ib.FormInfo.AdditionalForms = additionalForms
	return i
}

func (i *InsuranceBundleBuilder) WithVersion(version int64) *InsuranceBundleBuilder {
	i.ib.Version = version
	return i
}

func (i *InsuranceBundleBuilder) UnsetSegments() *InsuranceBundleBuilder {
	i.ib.Segments = nil
	return i
}

func (i *InsuranceBundleBuilder) WithState(state InsuranceBundleState) *InsuranceBundleBuilder {
	i.ib.State = state
	return i
}

func (i *InsuranceBundleBuilder) UnsetCheckpointTimestamps() *InsuranceBundleBuilder {
	i.ib.CreatedAt = timestamppb.New(time.Time{})
	i.ib.UpdatedAt = timestamppb.New(time.Time{})
	return i
}

func (i *InsuranceBundleBuilder) WithCarrierAdmittedType(
	carrierAdmittedType insurancecoreproto.CarrierAdmittedType,
) *InsuranceBundleBuilder {
	i.ib.CarrierAdmittedType = carrierAdmittedType
	return i
}

func (i *InsuranceBundleBuilder) WithDefaultEffectiveDuration(interval *proto.Interval) *InsuranceBundleBuilder {
	i.ib.DefaultEffectiveDuration = interval
	return i
}

func (i *InsuranceBundleBuilder) WithDefaultCarrier(
	carrier insurancecoreproto.InsuranceCarrier,
) *InsuranceBundleBuilder {
	i.ib.DefaultCarrier = carrier
	return i
}

func (i *InsuranceBundleBuilder) WithDefaultSeller(seller *insurancecoreproto.SellerInfo) *InsuranceBundleBuilder {
	i.ib.DefaultSeller = seller
	return i
}

func (i *InsuranceBundleBuilder) WithMetadata(metadata *InsuranceBundleMetadata) *InsuranceBundleBuilder {
	i.ib.Metadata = metadata
	return i
}

func (i *InsuranceBundleBuilder) WithFormInfo(formInfo *insurancecoreproto.FormInfo) *InsuranceBundleBuilder {
	i.ib.FormInfo = formInfo
	return i
}

type InsuranceBundleSegmentBuilder struct {
	ibSegment *InsuranceBundleSegment
}

// NewInsuranceBundleSegmentBuilder gives you a new InsuranceBundleSegmentBuilder with default values. You can override
// these values by using the With* methods.
func NewInsuranceBundleSegmentBuilder(programType insurancecoreproto.ProgramType) *InsuranceBundleSegmentBuilder {
	policy1 := NewPolicyBuilder(programType).Build()

	policy2Coverages := []*Coverage{
		{
			Id:          "CoverageMotorTruckCargo",
			DisplayName: "Motor Truck Cargo",
			SubCoverages: []*SubCoverage{
				{
					Id:          "CoverageMotorTruckCargo",
					DisplayName: "Cargo Collision",
				},
			},
		},
	}

	mtcCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("15", timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime())).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Cargo).
		Build()
	policy2 := NewPolicyBuilder(programType).
		WithPolicyNumber("NNFMC0012345-24").
		WithCharges([]*ptypes.Charge{mtcCharge}).
		WithCoverages(policy2Coverages).
		Build()

	policy3Coverages := []*Coverage{
		{
			Id:          "CoverageGeneralLiability",
			DisplayName: "General Liability",
			SubCoverages: []*SubCoverage{
				{
					Id:          "CoverageGeneralLiability",
					DisplayName: "General Liability",
				},
			},
		},
	}

	glCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("20", timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime())).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
		Build()
	policy3 := NewPolicyBuilder(programType).
		WithPolicyNumber("NNFGL0012345-24").
		WithCharges([]*ptypes.Charge{glCharge}).
		WithCoverages(policy3Coverages).
		Build()

	return &InsuranceBundleSegmentBuilder{ibSegment: &InsuranceBundleSegment{
		Id: uuid.NewString(),
		Interval: &proto.Interval{
			Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
			End:   timestamppb.New(time_utils.NewDate(2025, 5, 1).ToTime()),
		},
		PrimaryInsured: &insurancecoreproto.Insured{
			Id:   uuid.NewString(),
			Type: insurancecoreproto.InsuredType_InsuredType_PrimaryInsured,
			Name: &insurancecoreproto.InsuredName{
				BusinessName: "Nirvana Trucking Solutions",
			},
			Address: &proto.Address{
				Nation:  pointer_utils.ToPointer("US"),
				State:   pointer_utils.ToPointer("CA"),
				City:    pointer_utils.ToPointer("San Francisco"),
				Street:  pointer_utils.ToPointer("1234 Main St"),
				ZipCode: pointer_utils.ToPointer("94111"),
			},
			ExternalIdentifier: &insurancecoreproto.InsuredIdentifier{
				Type:  insurancecoreproto.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
				Value: []string{"123456"},
			},
		},
		Policies: map[string]*Policy{
			"NNFTK0012345-24": policy1,
			"NNFMC0012345-24": policy2,
			"NNFGL0012345-24": policy3,
		},
		CoverageCriteria: &CoverageCriteria{
			Limits: []*Limit{
				{
					Id:             "CoverageGeneralLiability",
					DisplayName:    "General Liability",
					SubCoverageIds: []string{"CoverageGeneralLiability"},
					Amount:         2000000,
					Grouping:       LimitGrouping_LimitGrouping_Single,
				},
				{
					Id:             "CoverageAutoLiability",
					DisplayName:    "Auto Liability",
					SubCoverageIds: []string{"CoverageBodilyInjury", "CoveragePropertyDamage"},
					Amount:         1000000,
					Grouping:       LimitGrouping_LimitGrouping_Combined,
				},
				{
					Id:             "CoverageMotorTruckCargo",
					DisplayName:    "Motor Truck Cargo",
					SubCoverageIds: []string{"CoverageMotorTruckCargo"},
					Amount:         100000,
					Grouping:       LimitGrouping_LimitGrouping_Single,
				},
				{
					Id:             "CoverageUIM",
					DisplayName:    "UIM",
					SubCoverageIds: []string{"CoverageUIM"},
					Amount:         70000,
					Grouping:       LimitGrouping_LimitGrouping_Single,
				},
				{
					Id:             "CoverageTowingLaborAndStorage",
					DisplayName:    "Towing Labor And Storage",
					SubCoverageIds: []string{"CoverageTowingLaborAndStorage"},
					Amount:         15000,
					Grouping:       LimitGrouping_LimitGrouping_Single,
				},
				{
					Id:             "CoverageRentalReimbursement",
					DisplayName:    "Rental Reimbursement",
					SubCoverageIds: []string{"CoverageRentalReimbursement"},
					Amount:         3000,
					Grouping:       LimitGrouping_LimitGrouping_Single,
				},
				{
					Id:             "CoverageUM",
					DisplayName:    "UM",
					SubCoverageIds: []string{"CoverageUM"},
					Amount:         70000,
					Grouping:       LimitGrouping_LimitGrouping_Single,
				},
			},
			Deductibles: []*Deductible{
				{
					SubCoverageIds: []string{"CoverageGeneralLiability"},
				},
				{
					SubCoverageIds: []string{"CoverageBodilyInjury", "CoveragePropertyDamage"},
				},
				{
					SubCoverageIds: []string{"CoverageCollision"},
					Amount:         1000,
				},
				{
					SubCoverageIds: []string{"CoverageComprehensive"},
					Amount:         1000,
				},
				{
					SubCoverageIds: []string{"CoverageMotorTruckCargo"},
					Amount:         1000,
				},
				{
					SubCoverageIds: []string{"CoverageUIM"},
				},
				{
					SubCoverageIds: []string{"CoverageTowingLaborAndStorage"},
				},
				{
					SubCoverageIds: []string{"CoverageRentalReimbursement"},
				},
				{
					SubCoverageIds: []string{"CoverageUM"},
				},
			},
			CombinedDeductibles: []*CombinedDeductible{
				{
					SubCoverageIds: []string{"CoverageCollision", "CoverageComprehensive", "CoverageMotorTruckCargo"},
				},
			},
		},
	}}
}

func (i *InsuranceBundleSegmentBuilder) Build() *InsuranceBundleSegment {
	return i.ibSegment
}

func (i *InsuranceBundleSegmentBuilder) WithPolicies(policies map[string]*Policy) *InsuranceBundleSegmentBuilder {
	i.ibSegment.Policies = policies
	return i
}

func (i *InsuranceBundleSegmentBuilder) WithInterval(interval *proto.Interval) *InsuranceBundleSegmentBuilder {
	i.ibSegment.Interval = interval
	return i
}

func (i *InsuranceBundleSegmentBuilder) UnsetPolicies() *InsuranceBundleSegmentBuilder {
	i.ibSegment.Policies = map[string]*Policy{}
	return i
}

func (i *InsuranceBundleSegmentBuilder) WithDeductibles(deductibles []*Deductible) *InsuranceBundleSegmentBuilder {
	i.ibSegment.CoverageCriteria.Deductibles = deductibles
	return i
}

func (i *InsuranceBundleSegmentBuilder) WithLimits(limits []*Limit) *InsuranceBundleSegmentBuilder {
	i.ibSegment.CoverageCriteria.Limits = limits
	return i
}

func (i *InsuranceBundleSegmentBuilder) WithCoverageCriteria(coverageCriteria *CoverageCriteria) *InsuranceBundleSegmentBuilder {
	i.ibSegment.CoverageCriteria = coverageCriteria
	return i
}

func (i *InsuranceBundleSegmentBuilder) WithPrimaryInsured(primaryInsured *insurancecoreproto.Insured) *InsuranceBundleSegmentBuilder {
	i.ibSegment.PrimaryInsured = primaryInsured
	return i
}

// WithId overrides the auto-generated ID for deterministic tests.
func (i *InsuranceBundleSegmentBuilder) WithId(id string) *InsuranceBundleSegmentBuilder {
	i.ibSegment.Id = id
	return i
}

// WithNoticeOfCancellation sets the NoticeOfCancellation for the segment.
func (i *InsuranceBundleSegmentBuilder) WithNoticeOfCancellation(noc *NoticeOfCancellation) *InsuranceBundleSegmentBuilder {
	i.ibSegment.NoticeOfCancellation = noc
	return i
}

type LimitBuilder struct {
	limit *Limit
}

func NewLimitBuilder() *LimitBuilder {
	return &LimitBuilder{
		limit: &Limit{
			Id:             "BodilyInjuryPerPerson",
			DisplayName:    "Bodily Injury Per Person",
			SubCoverageIds: []string{"CoverageBodilyInjury"},
			Amount:         40000,
			Grouping:       LimitGrouping_LimitGrouping_Single,
		},
	}
}

func (l *LimitBuilder) Build() *Limit {
	return l.limit
}

func (l *LimitBuilder) WithId(id string) *LimitBuilder {
	l.limit.Id = id
	return l
}

func (l *LimitBuilder) WithDisplayName(displayName string) *LimitBuilder {
	l.limit.DisplayName = displayName
	return l
}

func (l *LimitBuilder) WithSubCoverageIds(subCoverageIds []string) *LimitBuilder {
	l.limit.SubCoverageIds = subCoverageIds
	return l
}

func (l *LimitBuilder) WithAmount(amount float64) *LimitBuilder {
	l.limit.Amount = amount
	return l
}

func GetMockCondensedInsuranceBundle() *CondensedInsuranceBundle {
	return &CondensedInsuranceBundle{
		InternalID: uuid.NewString(),
		ExternalID: "mock-id",
		PrimaryInsured: &insurancecoreproto.Insured{
			Id:   uuid.NewString(),
			Type: insurancecoreproto.InsuredType_InsuredType_PrimaryInsured,
			Name: &insurancecoreproto.InsuredName{
				BusinessName: "Nirvana Trucking Solutions",
			},
			Address: &proto.Address{
				Nation:  pointer_utils.ToPointer("US"),
				State:   pointer_utils.ToPointer("CA"),
				City:    pointer_utils.ToPointer("San Francisco"),
				Street:  pointer_utils.ToPointer("1234 Main St"),
				ZipCode: pointer_utils.ToPointer("94111"),
			},
			ExternalIdentifier: &insurancecoreproto.InsuredIdentifier{
				Type:  insurancecoreproto.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
				Value: []string{"123456"},
			},
		},
		DefaultEffectiveDuration: &proto.Interval{
			Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
			End:   timestamppb.New(time_utils.NewDate(2025, 12, 1).ToTime()),
		},
		State: InsuranceBundleState_InsuranceBundleState_Active,
	}
}
